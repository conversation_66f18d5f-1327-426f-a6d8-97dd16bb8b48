# OAuth 2.0 Token Exchange - Technical Implementation Specification

## Overview

This document provides detailed technical implementation guidance for the OAuth 2.0 Token Exchange feature. It complements the [functional specification](20250628-token-exchange.md) with concrete code patterns, architectural decisions, and implementation details for AI coding assistants.

## Current Testing Infrastructure Analysis

### Token Generation and Validation Testing

Based on analysis of the current codebase, the application uses a sophisticated testing infrastructure for token validation:

#### Test Token Generation Pattern
- **Location**: `crates/services/tests/data/` contains test certificates and keys
- **Key Files**: `test_private_key.pem`, `test_public_key.pem` for JWT signing/verification
- **Token Creation**: Tests generate valid JWT tokens using private keys for realistic testing
- **Integration Testing**: Full end-to-end token validation with actual JWT parsing and validation

#### Current Test Utilities
```rust
// Pattern from existing tests - token generation utilities
pub fn create_test_access_token(
  user_id: &str,
  client_id: &str,
  scopes: Vec<String>,
  exp_offset_seconds: i64,
) -> String {
  // Generate JWT with test private key
  // Include standard claims: iss, aud, sub, exp, iat, jti, azp, scope
  // Return signed JWT token
}

pub fn create_expired_token(user_id: &str, client_id: &str) -> String {
  // Create token with past expiration time
}

pub fn create_cross_client_token(
  user_id: &str,
  subject_client_id: &str,  // Different from our client
  target_issuer: &str,
) -> String {
  // Create token for cross-client testing
}
```

#### Integration Test Patterns
```rust
// Pattern from existing integration tests
#[tokio::test]
async fn test_token_validation_integration() {
  // 1. Set up test environment with encrypted secrets
  let app_service = create_test_app_service().await;
  
  // 2. Generate valid test token
  let token = create_test_access_token("user123", "test-client", vec!["openid".to_string()], 3600);
  
  // 3. Store token in database for validation
  let token_service = app_service.token_service();
  token_service.store_token(&token).await.unwrap();
  
  // 4. Test validation
  let result = token_service.validate_bearer_token(&format!("Bearer {}", token)).await;
  assert!(result.is_ok());
}
```

## Enhanced Token Service Architecture

### Core Service Structure
```rust
pub struct DefaultTokenService {
  auth_service: Arc<dyn AuthService>,
  secret_service: Arc<dyn SecretService>,
  cache_service: Arc<dyn CacheService>,
  db_service: Arc<dyn DbService>,
  issuer_validator: IssuerValidator,
  rate_limiter: Arc<dyn RateLimiter>,
  settings: TokenExchangeSettings,
}

impl DefaultTokenService {
  // Enhanced validation supporting cross-client tokens
  pub async fn validate_bearer_token(&self, header: &str) -> Result<(String, TokenScope), AuthError> {
    let token = self.extract_bearer_token(header)?;
    
    // Try existing database validation first (backward compatibility)
    if let Ok(result) = self.validate_database_token(&token).await {
      return Ok(result);
    }
    
    // If not found and cross-client exchange is enabled, try token exchange
    if self.settings.cross_client_enabled {
      return self.validate_and_exchange_cross_client_token(&token).await;
    }
    
    Err(AuthError::TokenNotFound)
  }
  
  // New method for cross-client token validation and exchange
  async fn validate_and_exchange_cross_client_token(&self, token: &str) -> Result<(String, TokenScope), AuthError> {
    // 1. Parse JWT claims (no signature verification - trust Keycloak)
    let claims = extract_claims::<Claims>(token)?;
    
    // 2. Validate token expiration with safety buffer
    self.validate_token_expiration(&claims)?;
    
    // 3. Validate issuer matches our Keycloak instance
    self.issuer_validator.validate(&claims.iss)?;
    
    // 4. Check rate limiting for this client
    self.rate_limiter.check_rate_limit(&claims.azp, "token_exchange").await?;
    
    // 5. Check cache for previously exchanged token
    let cache_key = self.generate_exchange_cache_key(&claims.jti, token)?;
    if let Some(cached_result) = self.get_cached_exchange_token(&cache_key).await? {
      return Ok(cached_result);
    }
    
    // 6. Exchange token with Keycloak
    let exchanged_token = self.exchange_cross_client_token(token).await?;
    
    // 7. Cache the exchanged token
    self.cache_exchanged_token(&cache_key, &exchanged_token, token).await?;
    
    // 8. Extract scope and return
    let scope = self.extract_token_scope(&exchanged_token).await?;
    Ok((exchanged_token, scope))
  }
}
```

### Token Expiration Validation with 401 Response
```rust
impl DefaultTokenService {
  // Validate token expiration with proper error response
  fn validate_token_expiration(&self, claims: &Claims) -> Result<(), AuthError> {
    let now = Utc::now().timestamp() as u64;
    let safety_buffer = 60; // 1 minute safety buffer
    
    if claims.exp <= now + safety_buffer {
      return Err(AuthError::TokenExpired {
        expires_at: DateTime::<Utc>::from_timestamp(claims.exp as i64, 0)
          .unwrap_or_else(|| Utc::now()),
        current_time: Utc::now(),
      });
    }
    
    Ok(())
  }
}

// Enhanced error type for token expiration
#[derive(Debug, thiserror::Error, errmeta_derive::ErrorMeta)]
#[error_meta(trait_to_impl = AppError)]
pub enum AuthError {
  #[error("token expired at {expires_at}, current time: {current_time}")]
  #[error_meta(error_type = ErrorType::Authentication)]
  TokenExpired {
    expires_at: DateTime<Utc>,
    current_time: DateTime<Utc>,
  },
  // ... other error variants
}

// HTTP response mapping for token expiration
impl From<AuthError> for ApiError {
  fn from(auth_error: AuthError) -> Self {
    match auth_error {
      AuthError::TokenExpired { expires_at, .. } => {
        ApiError::Unauthorized {
          error: "token_expired".to_string(),
          error_description: "The provided token has expired".to_string(),
          expires_at: Some(expires_at),
        }
      }
      // ... other mappings
    }
  }
}
```

### Secure Token Caching Implementation
```rust
// Cache data structure with security metadata
#[derive(Serialize, Deserialize, Debug)]
pub struct ExchangeTokenCache {
  access_token: String,
  exp: u64,
  cached_at: u64,
  subject_client: String,
  subject_jti: String,
  exchange_count: u32,
  last_validated: u64,
}

impl DefaultTokenService {
  // Generate secure cache key for exchanged tokens
  fn generate_exchange_cache_key(&self, jti: &str, token: &str) -> Result<String, AuthError> {
    use sha2::{Digest, Sha256};
    
    // Create composite hash from token content and metadata
    let token_hash = format!("{:x}", Sha256::digest(token.as_bytes()));
    let hash_prefix = &token_hash[..16]; // 16 chars for collision resistance
    
    // Include time bucket for cache rotation (1-hour buckets)
    let now = Utc::now().timestamp();
    let time_bucket = now / 3600;
    
    Ok(format!("exchange:{}:{}:{}", jti, hash_prefix, time_bucket))
  }
  
  // Cache exchanged token with comprehensive metadata
  async fn cache_exchanged_token(&self, cache_key: &str, token: &str, subject_token: &str) -> Result<(), AuthError> {
    let exchanged_claims = extract_claims::<Claims>(token)?;
    let subject_claims = extract_claims::<Claims>(subject_token)?;
    let now = Utc::now().timestamp() as u64;
    
    // Don't cache tokens expiring soon
    if exchanged_claims.exp <= now + 300 {
      return Ok(());
    }
    
    let cache_entry = ExchangeTokenCache {
      access_token: token.to_string(),
      exp: exchanged_claims.exp,
      cached_at: now,
      subject_client: subject_claims.azp.clone(),
      subject_jti: subject_claims.jti.clone(),
      exchange_count: 1,
      last_validated: now,
    };
    
    let cache_value = serde_json::to_string(&cache_entry)?;
    self.cache_service.set(cache_key, &cache_value);
    
    // Audit log cache operation
    tracing::info!(
      "Cached exchanged token: subject_client={}, subject_jti={}, exp={}",
      cache_entry.subject_client,
      cache_entry.subject_jti,
      cache_entry.exp
    );
    
    Ok(())
  }
  
  // Validate and retrieve cached exchanged token
  async fn get_cached_exchange_token(&self, cache_key: &str) -> Result<Option<(String, TokenScope)>, AuthError> {
    let Some(cached_value) = self.cache_service.get(cache_key) else {
      return Ok(None);
    };
    
    let mut cache_entry: ExchangeTokenCache = serde_json::from_str(&cached_value)
      .map_err(|_| AuthError::InvalidCacheData)?;
    
    let now = Utc::now().timestamp() as u64;
    
    // Check expiration with safety buffer
    if cache_entry.exp <= now + 60 {
      self.cache_service.remove(cache_key);
      return Ok(None);
    }
    
    // Check for suspicious usage patterns
    if cache_entry.exchange_count > 1000 {
      tracing::warn!(
        "High exchange count detected: subject_jti={}, count={}",
        cache_entry.subject_jti,
        cache_entry.exchange_count
      );
      self.cache_service.remove(cache_key);
      return Err(AuthError::SuspiciousActivity);
    }
    
    // Update usage statistics
    cache_entry.exchange_count += 1;
    cache_entry.last_validated = now;
    
    // Re-cache updated entry
    let updated_cache = serde_json::to_string(&cache_entry)?;
    self.cache_service.set(cache_key, &updated_cache);
    
    // Extract scope from cached token
    let claims = extract_claims::<Claims>(&cache_entry.access_token)?;
    let scope = TokenScope::from_scope(&claims.scope)?;
    
    tracing::debug!(
      "Cache hit for exchanged token: subject_jti={}, usage_count={}",
      cache_entry.subject_jti,
      cache_entry.exchange_count
    );
    
    Ok(Some((cache_entry.access_token, scope)))
  }
}
```

### Issuer Validation Implementation
```rust
pub struct IssuerValidator {
  expected_issuer: String,
  realm: String,
}

impl IssuerValidator {
  pub fn new(auth_url: &str, realm: &str) -> Self {
    let expected_issuer = format!("{}/realms/{}", auth_url.trim_end_matches('/'), realm);
    Self {
      expected_issuer,
      realm: realm.to_string(),
    }
  }
  
  pub fn validate(&self, token_issuer: &str) -> Result<(), AuthError> {
    if token_issuer != self.expected_issuer {
      return Err(AuthError::InvalidIssuer {
        expected: self.expected_issuer.clone(),
        actual: token_issuer.to_string(),
      });
    }
    Ok(())
  }
  
  pub fn expected_issuer(&self) -> &str {
    &self.expected_issuer
  }
}
```

### Cross-Client Token Exchange Implementation
```rust
impl DefaultTokenService {
  // Exchange cross-client token for our client token
  async fn exchange_cross_client_token(&self, subject_token: &str) -> Result<String, AuthError> {
    let app_reg_info = self.secret_service
      .app_reg_info()?
      .ok_or(AuthError::AppRegInfoMissing)?;
    
    // Use existing AuthService::exchange_token method (RFC 8693)
    let (access_token, _refresh_token) = self.auth_service
      .exchange_token(
        &app_reg_info.client_id,
        &app_reg_info.client_secret,
        subject_token,
        "urn:ietf:params:oauth:token-type:access_token",
        vec!["openid".to_string()], // Minimal scope for validation
      )
      .await
      .map_err(|e| self.handle_exchange_error(e))?;
    
    Ok(access_token)
  }
  
  // Handle token exchange errors gracefully
  fn handle_exchange_error(&self, error: AuthServiceError) -> AuthError {
    match error {
      AuthServiceError::TokenExchangeError(msg) if msg.contains("invalid_grant") => {
        AuthError::CrossClientValidationFailed("Token not valid for exchange".to_string())
      }
      AuthServiceError::TokenExchangeError(msg) if msg.contains("invalid_client") => {
        AuthError::CrossClientValidationFailed("Client not authorized for exchange".to_string())
      }
      AuthServiceError::TokenExchangeError(msg) if msg.contains("unsupported_token_type") => {
        AuthError::CrossClientValidationFailed("Token type not supported for exchange".to_string())
      }
      _ => AuthError::TokenExchangeFailed(error.to_string()),
    }
  }
}
```

## Rate Limiting Implementation

### Rate Limiter Interface and Implementation
```rust
#[async_trait]
pub trait RateLimiter: Send + Sync + std::fmt::Debug {
  async fn check_rate_limit(&self, client_id: &str, operation: &str) -> Result<(), AuthError>;
  async fn record_operation(&self, client_id: &str, operation: &str) -> Result<(), AuthError>;
}

pub struct TokenExchangeRateLimiter {
  cache_service: Arc<dyn CacheService>,
  max_requests_per_minute: u32,
  window_size_seconds: u64,
}

impl TokenExchangeRateLimiter {
  pub fn new(cache_service: Arc<dyn CacheService>, max_requests_per_minute: u32) -> Self {
    Self {
      cache_service,
      max_requests_per_minute,
      window_size_seconds: 60,
    }
  }

  fn rate_limit_key(&self, client_id: &str, operation: &str) -> String {
    let now = Utc::now().timestamp();
    let window = now / self.window_size_seconds as i64;
    format!("rate_limit:{}:{}:{}", operation, client_id, window)
  }
}

#[async_trait]
impl RateLimiter for TokenExchangeRateLimiter {
  async fn check_rate_limit(&self, client_id: &str, operation: &str) -> Result<(), AuthError> {
    let key = self.rate_limit_key(client_id, operation);

    let current_count: u32 = self.cache_service
      .get(&key)
      .and_then(|v| v.parse().ok())
      .unwrap_or(0);

    if current_count >= self.max_requests_per_minute {
      tracing::warn!(
        "Rate limit exceeded for client {} operation {}: {} requests",
        client_id, operation, current_count
      );
      return Err(AuthError::TokenExchangeRateLimit);
    }

    Ok(())
  }

  async fn record_operation(&self, client_id: &str, operation: &str) -> Result<(), AuthError> {
    let key = self.rate_limit_key(client_id, operation);

    let current_count: u32 = self.cache_service
      .get(&key)
      .and_then(|v| v.parse().ok())
      .unwrap_or(0);

    let new_count = current_count + 1;
    self.cache_service.set(&key, &new_count.to_string());

    Ok(())
  }
}
```

## Enhanced Error Types

### Comprehensive Error Handling
```rust
#[derive(Debug, thiserror::Error, errmeta_derive::ErrorMeta)]
#[error_meta(trait_to_impl = AppError)]
pub enum AuthError {
  // Existing errors...

  #[error("invalid issuer: expected {expected}, got {actual}")]
  #[error_meta(error_type = ErrorType::Authentication)]
  InvalidIssuer { expected: String, actual: String },

  #[error("token exchange failed: {0}")]
  #[error_meta(error_type = ErrorType::Authentication)]
  TokenExchangeFailed(String),

  #[error("invalid cache data format")]
  #[error_meta(error_type = ErrorType::InternalServerError)]
  InvalidCacheData,

  #[error("cross-client token validation failed: {0}")]
  #[error_meta(error_type = ErrorType::Authentication)]
  CrossClientValidationFailed(String),

  #[error("token expired at {expires_at}, current time: {current_time}")]
  #[error_meta(error_type = ErrorType::Authentication)]
  TokenExpired {
    expires_at: DateTime<Utc>,
    current_time: DateTime<Utc>,
  },

  #[error("suspicious activity detected")]
  #[error_meta(error_type = ErrorType::Authentication)]
  SuspiciousActivity,

  #[error("invalid cache key format")]
  #[error_meta(error_type = ErrorType::InternalServerError)]
  InvalidCacheKey,

  #[error("cache key mismatch with token")]
  #[error_meta(error_type = ErrorType::Authentication)]
  CacheKeyMismatch,

  #[error("rate limit exceeded for token exchange")]
  #[error_meta(error_type = ErrorType::RateLimit)]
  TokenExchangeRateLimit,

  #[error("app registration info missing")]
  #[error_meta(error_type = ErrorType::Configuration)]
  AppRegInfoMissing,
}

// HTTP response mapping for enhanced errors
impl From<AuthError> for ApiError {
  fn from(auth_error: AuthError) -> Self {
    match auth_error {
      AuthError::TokenExpired { expires_at, .. } => {
        ApiError::Unauthorized {
          error: "token_expired".to_string(),
          error_description: "The provided token has expired".to_string(),
          expires_at: Some(expires_at),
        }
      }
      AuthError::InvalidIssuer { expected, actual } => {
        ApiError::Unauthorized {
          error: "invalid_issuer".to_string(),
          error_description: format!("Token issuer '{}' not authorized", actual),
          expires_at: None,
        }
      }
      AuthError::TokenExchangeRateLimit => {
        ApiError::TooManyRequests {
          error: "rate_limit_exceeded".to_string(),
          error_description: "Too many token exchange requests".to_string(),
          retry_after: Some(60), // seconds
        }
      }
      AuthError::CrossClientValidationFailed(msg) => {
        ApiError::Unauthorized {
          error: "cross_client_validation_failed".to_string(),
          error_description: msg,
          expires_at: None,
        }
      }
      _ => ApiError::InternalServerError,
    }
  }
}
```

## Configuration and Settings

### Settings Service Integration
```rust
#[derive(Debug, Clone)]
pub struct TokenExchangeSettings {
  pub cross_client_enabled: bool,
  pub cache_ttl_seconds: u64,
  pub rate_limit_per_minute: u32,
  pub max_cache_size: usize,
  pub audit_enabled: bool,
  pub suspicious_threshold: u32,
}

impl SettingService {
  // Token exchange configuration methods
  pub fn token_exchange_enabled(&self) -> bool {
    self.get_bool("TOKEN_EXCHANGE_ENABLED").unwrap_or(true)
  }

  pub fn token_exchange_cache_ttl(&self) -> u64 {
    self.get_u64("TOKEN_EXCHANGE_CACHE_TTL").unwrap_or(3600)
  }

  pub fn token_exchange_rate_limit(&self) -> u32 {
    self.get_u32("TOKEN_EXCHANGE_RATE_LIMIT").unwrap_or(100)
  }

  pub fn token_exchange_max_cache_size(&self) -> usize {
    self.get_usize("TOKEN_EXCHANGE_MAX_CACHE_SIZE").unwrap_or(10000)
  }

  pub fn token_exchange_audit_enabled(&self) -> bool {
    self.get_bool("TOKEN_EXCHANGE_AUDIT_ENABLED").unwrap_or(true)
  }

  pub fn token_exchange_suspicious_threshold(&self) -> u32 {
    self.get_u32("TOKEN_EXCHANGE_SUSPICIOUS_THRESHOLD").unwrap_or(1000)
  }

  // Build complete settings structure
  pub fn token_exchange_settings(&self) -> TokenExchangeSettings {
    TokenExchangeSettings {
      cross_client_enabled: self.token_exchange_enabled(),
      cache_ttl_seconds: self.token_exchange_cache_ttl(),
      rate_limit_per_minute: self.token_exchange_rate_limit(),
      max_cache_size: self.token_exchange_max_cache_size(),
      audit_enabled: self.token_exchange_audit_enabled(),
      suspicious_threshold: self.token_exchange_suspicious_threshold(),
    }
  }
}
```

## Testing Implementation Patterns

### Enhanced Test Utilities for Cross-Client Token Testing

Based on the current testing infrastructure analysis, here are the enhanced test utilities needed for cross-client token exchange testing:

```rust
// Enhanced test utilities in crates/services/src/test_utils/auth.rs
use crate::{AppRegInfoBuilder, KeycloakAuthService, TOKEN_TYPE_BEARER, TOKEN_TYPE_OFFLINE};

pub const CROSS_CLIENT_ID: &str = "third-party-client";
pub const CROSS_CLIENT_SECRET: &str = "third-party-secret";
pub const INVALID_ISSUER: &str = "https://malicious.example.com/realms/evil";

// Create cross-client token with different azp claim
pub fn create_cross_client_token(
  user_id: &str,
  client_id: &str,
  issuer: &str,
  exp_offset_seconds: i64,
) -> String {
  let claims = cross_client_token_claims(user_id, client_id, issuer, exp_offset_seconds);
  let (token, _) = build_token(claims).expect("Failed to build cross-client token");
  token
}

pub fn cross_client_token_claims(
  user_id: &str,
  client_id: &str,
  issuer: &str,
  exp_offset_seconds: i64,
) -> Value {
  let now = Utc::now().timestamp();
  json!({
    "exp": now + exp_offset_seconds,
    "iat": now,
    "jti": Uuid::new_v4().to_string(),
    "iss": issuer,
    "sub": user_id,
    "typ": TOKEN_TYPE_BEARER,
    "azp": client_id,  // Different from TEST_CLIENT_ID
    "session_state": Uuid::new_v4().to_string(),
    "resource_access": {
      client_id: {
        "roles": [
          "resource_user",
          "resource_power_user"
        ]
      }
    },
    "scope": "openid profile email roles",
    "sid": Uuid::new_v4().to_string(),
    "email_verified": true,
    "name": "Cross Client User",
    "preferred_username": "<EMAIL>",
    "given_name": "Cross",
    "family_name": "Client",
    "email": "<EMAIL>"
  })
}

// Create token with invalid issuer for security testing
pub fn create_invalid_issuer_token(
  user_id: &str,
  client_id: &str,
  exp_offset_seconds: i64,
) -> String {
  create_cross_client_token(user_id, client_id, INVALID_ISSUER, exp_offset_seconds)
}

// Create expired cross-client token
pub fn create_expired_cross_client_token(
  user_id: &str,
  client_id: &str,
  issuer: &str,
) -> String {
  create_cross_client_token(user_id, client_id, issuer, -3600) // Expired 1 hour ago
}

// Create token with different signing key for security testing
pub fn create_malicious_token(
  user_id: &str,
  client_id: &str,
  issuer: &str,
  exp_offset_seconds: i64,
) -> String {
  let claims = cross_client_token_claims(user_id, client_id, issuer, exp_offset_seconds);
  let (token, _) = sign_token(&OTHER_PRIVATE_KEY, &OTHER_PUBLIC_KEY, claims)
    .expect("Failed to build malicious token");
  token
}

// Mock AuthService for token exchange testing
pub fn create_mock_auth_service_for_exchange() -> MockAuthService {
  let mut mock_auth_service = MockAuthService::new();

  // Mock successful token exchange
  mock_auth_service
    .expect_exchange_token()
    .returning(|_, _, subject_token, _, _| {
      // Parse subject token to extract user info
      let claims = extract_claims::<Claims>(subject_token)?;

      // Return exchanged token for our client
      let exchanged_claims = json!({
        "exp": Utc::now().timestamp() + 3600,
        "iat": Utc::now().timestamp(),
        "jti": Uuid::new_v4().to_string(),
        "iss": ISSUER,
        "sub": claims.sub,
        "typ": TOKEN_TYPE_BEARER,
        "azp": TEST_CLIENT_ID,  // Our client ID
        "scope": "openid profile email",
        "sid": Uuid::new_v4().to_string(),
      });

      let (exchanged_token, _) = build_token(exchanged_claims)?;
      Ok((exchanged_token, None))
    });

  mock_auth_service
}

// Mock AuthService that fails token exchange
pub fn create_failing_mock_auth_service() -> MockAuthService {
  let mut mock_auth_service = MockAuthService::new();

  mock_auth_service
    .expect_exchange_token()
    .returning(|_, _, _, _, _| {
      Err(AuthServiceError::TokenExchangeError("invalid_grant".to_string()))
    });

  mock_auth_service
}
```

### Unit Test Patterns for Cross-Client Token Exchange

```rust
#[cfg(test)]
mod cross_client_tests {
  use super::*;
  use crate::test_utils::*;
  use mockall::predicate::*;
  use rstest::rstest;

  #[rstest]
  #[awt]
  #[tokio::test]
  async fn test_cross_client_token_validation_success(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
    #[future] test_db_service: TestDbService,
  ) -> anyhow::Result<()> {
    // Arrange
    let mock_auth_service = create_mock_auth_service_for_exchange();
    let mock_cache_service = MokaCacheService::default();
    let app_reg_info = AppRegInfoBuilder::test_default().build()?;
    let secret_service = SecretServiceStub::default().with_app_reg_info(&app_reg_info);

    let token_service = DefaultTokenService::new(
      Arc::new(mock_auth_service),
      Arc::new(secret_service),
      Arc::new(mock_cache_service),
      Arc::new(test_db_service),
      IssuerValidator::new("https://id.mydomain.com", "myapp"),
      Arc::new(MockRateLimiter::new()),
      TokenExchangeSettings::default(),
    );

    // Create cross-client token (different azp than our client)
    let cross_client_token = create_cross_client_token(
      "user123",
      CROSS_CLIENT_ID,  // Different client
      ISSUER,           // Same issuer
      3600
    );

    // Act
    let result = token_service
      .validate_bearer_token(&format!("Bearer {}", cross_client_token))
      .await;

    // Assert
    assert!(result.is_ok());
    let (token, scope) = result.unwrap();
    assert!(!token.is_empty());
    assert_eq!(scope, TokenScope::User);
    Ok(())
  }

  #[rstest]
  #[awt]
  #[tokio::test]
  async fn test_invalid_issuer_rejection(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
    #[future] test_db_service: TestDbService,
  ) -> anyhow::Result<()> {
    // Arrange
    let token_service = create_test_token_service_with_cross_client_support(test_db_service).await;

    // Create token with wrong issuer
    let invalid_token = create_invalid_issuer_token("user123", CROSS_CLIENT_ID, 3600);

    // Act
    let result = token_service
      .validate_bearer_token(&format!("Bearer {}", invalid_token))
      .await;

    // Assert
    assert!(result.is_err());
    match result.unwrap_err() {
      AuthError::InvalidIssuer { expected, actual } => {
        assert_eq!(expected, ISSUER);
        assert_eq!(actual, INVALID_ISSUER);
      }
      _ => panic!("Expected InvalidIssuer error"),
    }
    Ok(())
  }

  #[rstest]
  #[awt]
  #[tokio::test]
  async fn test_token_expiry_with_401_response(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
    #[future] test_db_service: TestDbService,
  ) -> anyhow::Result<()> {
    // Arrange
    let token_service = create_test_token_service_with_cross_client_support(test_db_service).await;

    // Create expired token
    let expired_token = create_expired_cross_client_token("user123", CROSS_CLIENT_ID, ISSUER);

    // Act
    let result = token_service
      .validate_bearer_token(&format!("Bearer {}", expired_token))
      .await;

    // Assert
    assert!(result.is_err());
    match result.unwrap_err() {
      AuthError::TokenExpired { expires_at, current_time } => {
        assert!(expires_at < current_time);
      }
      _ => panic!("Expected TokenExpired error"),
    }

    // Test HTTP response mapping
    let api_error: ApiError = result.unwrap_err().into();
    match api_error {
      ApiError::Unauthorized { error, error_description, expires_at } => {
        assert_eq!(error, "token_expired");
        assert_eq!(error_description, "The provided token has expired");
        assert!(expires_at.is_some());
      }
      _ => panic!("Expected Unauthorized API error"),
    }
    Ok(())
  }

  #[rstest]
  #[awt]
  #[tokio::test]
  async fn test_cache_behavior_for_exchanged_tokens(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
    #[future] test_db_service: TestDbService,
  ) -> anyhow::Result<()> {
    // Arrange
    let mut mock_auth_service = create_mock_auth_service_for_exchange();

    // Expect only one call to exchange_token (second should hit cache)
    mock_auth_service
      .expect_exchange_token()
      .times(1)
      .returning(|_, _, _, _, _| {
        let exchanged_claims = json!({
          "exp": Utc::now().timestamp() + 3600,
          "iat": Utc::now().timestamp(),
          "jti": Uuid::new_v4().to_string(),
          "iss": ISSUER,
          "sub": "user123",
          "typ": TOKEN_TYPE_BEARER,
          "azp": TEST_CLIENT_ID,
          "scope": "openid profile email",
        });
        let (token, _) = build_token(exchanged_claims)?;
        Ok((token, None))
      });

    let token_service = create_token_service_with_mock_auth(mock_auth_service, test_db_service).await;
    let cross_client_token = create_cross_client_token("user123", CROSS_CLIENT_ID, ISSUER, 3600);

    // Act - First call should exchange token
    let result1 = token_service
      .validate_bearer_token(&format!("Bearer {}", cross_client_token))
      .await;

    // Act - Second call should hit cache
    let result2 = token_service
      .validate_bearer_token(&format!("Bearer {}", cross_client_token))
      .await;

    // Assert
    assert!(result1.is_ok());
    assert!(result2.is_ok());

    // Both should return the same token (from cache)
    let (token1, _) = result1.unwrap();
    let (token2, _) = result2.unwrap();
    assert_eq!(token1, token2);
    Ok(())
  }

  #[rstest]
  #[awt]
  #[tokio::test]
  async fn test_rate_limiting_enforcement(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
    #[future] test_db_service: TestDbService,
  ) -> anyhow::Result<()> {
    // Arrange
    let mut mock_rate_limiter = MockRateLimiter::new();
    mock_rate_limiter
      .expect_check_rate_limit()
      .with(eq(CROSS_CLIENT_ID), eq("token_exchange"))
      .times(1)
      .returning(|_, _| Err(AuthError::TokenExchangeRateLimit));

    let token_service = create_token_service_with_rate_limiter(mock_rate_limiter, test_db_service).await;
    let cross_client_token = create_cross_client_token("user123", CROSS_CLIENT_ID, ISSUER, 3600);

    // Act
    let result = token_service
      .validate_bearer_token(&format!("Bearer {}", cross_client_token))
      .await;

    // Assert
    assert!(result.is_err());
    match result.unwrap_err() {
      AuthError::TokenExchangeRateLimit => {
        // Expected
      }
      _ => panic!("Expected TokenExchangeRateLimit error"),
    }
    Ok(())
  }

  #[rstest]
  #[awt]
  #[tokio::test]
  async fn test_token_exchange_failure_handling(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
    #[future] test_db_service: TestDbService,
  ) -> anyhow::Result<()> {
    // Arrange
    let failing_auth_service = create_failing_mock_auth_service();
    let token_service = create_token_service_with_mock_auth(failing_auth_service, test_db_service).await;
    let cross_client_token = create_cross_client_token("user123", CROSS_CLIENT_ID, ISSUER, 3600);

    // Act
    let result = token_service
      .validate_bearer_token(&format!("Bearer {}", cross_client_token))
      .await;

    // Assert
    assert!(result.is_err());
    match result.unwrap_err() {
      AuthError::CrossClientValidationFailed(msg) => {
        assert!(msg.contains("Token not valid for exchange"));
      }
      _ => panic!("Expected CrossClientValidationFailed error"),
    }
    Ok(())
  }

  #[rstest]
  #[awt]
  #[tokio::test]
  async fn test_cache_key_generation_security(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
    #[future] test_db_service: TestDbService,
  ) -> anyhow::Result<()> {
    // Arrange
    let token_service = create_test_token_service_with_cross_client_support(test_db_service).await;
    let token1 = create_cross_client_token("user1", "client1", ISSUER, 3600);
    let token2 = create_cross_client_token("user2", "client2", ISSUER, 3600);

    // Act
    let key1 = token_service.generate_exchange_cache_key("jti1", &token1)?;
    let key2 = token_service.generate_exchange_cache_key("jti2", &token2)?;

    // Assert
    assert_ne!(key1, key2);
    assert!(key1.starts_with("exchange:jti1:"));
    assert!(key2.starts_with("exchange:jti2:"));

    // Keys should include hash prefix and time bucket
    let parts1: Vec<&str> = key1.split(':').collect();
    let parts2: Vec<&str> = key2.split(':').collect();
    assert_eq!(parts1.len(), 4);
    assert_eq!(parts2.len(), 4);
    assert_eq!(parts1[0], "exchange");
    assert_eq!(parts2[0], "exchange");
    Ok(())
  }

  // Helper functions for test setup
  async fn create_test_token_service_with_cross_client_support(
    test_db_service: TestDbService,
  ) -> DefaultTokenService {
    let mock_auth_service = create_mock_auth_service_for_exchange();
    let mock_cache_service = MokaCacheService::default();
    let app_reg_info = AppRegInfoBuilder::test_default().build().unwrap();
    let secret_service = SecretServiceStub::default().with_app_reg_info(&app_reg_info);

    DefaultTokenService::new(
      Arc::new(mock_auth_service),
      Arc::new(secret_service),
      Arc::new(mock_cache_service),
      Arc::new(test_db_service),
      IssuerValidator::new("https://id.mydomain.com", "myapp"),
      Arc::new(MockRateLimiter::new()),
      TokenExchangeSettings::default(),
    )
  }

  async fn create_token_service_with_mock_auth(
    mock_auth_service: MockAuthService,
    test_db_service: TestDbService,
  ) -> DefaultTokenService {
    let mock_cache_service = MokaCacheService::default();
    let app_reg_info = AppRegInfoBuilder::test_default().build().unwrap();
    let secret_service = SecretServiceStub::default().with_app_reg_info(&app_reg_info);

    DefaultTokenService::new(
      Arc::new(mock_auth_service),
      Arc::new(secret_service),
      Arc::new(mock_cache_service),
      Arc::new(test_db_service),
      IssuerValidator::new("https://id.mydomain.com", "myapp"),
      Arc::new(MockRateLimiter::new()),
      TokenExchangeSettings::default(),
    )
  }

  async fn create_token_service_with_rate_limiter(
    mock_rate_limiter: MockRateLimiter,
    test_db_service: TestDbService,
  ) -> DefaultTokenService {
    let mock_auth_service = create_mock_auth_service_for_exchange();
    let mock_cache_service = MokaCacheService::default();
    let app_reg_info = AppRegInfoBuilder::test_default().build().unwrap();
    let secret_service = SecretServiceStub::default().with_app_reg_info(&app_reg_info);

    DefaultTokenService::new(
      Arc::new(mock_auth_service),
      Arc::new(secret_service),
      Arc::new(mock_cache_service),
      Arc::new(test_db_service),
      IssuerValidator::new("https://id.mydomain.com", "myapp"),
      Arc::new(mock_rate_limiter),
      TokenExchangeSettings::default(),
    )
  }
}
```

### Integration Test Patterns

Based on the current integration test infrastructure, here are the patterns for testing cross-client token exchange with live Keycloak:

```rust
// Integration tests in crates/integration-tests/tests/test_cross_client_token_exchange.rs
mod utils;

use crate::utils::{
  create_authenticated_session, create_session_cookie, get_oauth_tokens, live_server,
  TestServerHandle,
};
use axum::http::StatusCode;
use pretty_assertions::assert_eq;
use serde_json::Value;
use std::time::Duration;

#[rstest::rstest]
#[awt]
#[tokio::test]
#[timeout(Duration::from_secs(5 * 60))]
#[serial_test::serial(live)]
async fn test_live_cross_client_token_exchange(
  #[future] live_server: anyhow::Result<TestServerHandle>,
) -> anyhow::Result<()> {
  let TestServerHandle {
    temp_cache_dir: _temp_cache_dir,
    host,
    port,
    handle,
    app_service,
  } = live_server?;

  // Get OAuth tokens for our primary client
  let (primary_access_token, _) = get_oauth_tokens(app_service.as_ref()).await?;

  // Get OAuth tokens for a different client (cross-client scenario)
  let (cross_client_token, _) = get_cross_client_oauth_tokens(app_service.as_ref()).await?;

  // Test 1: Primary client token should work (existing functionality)
  let response = reqwest::Client::new()
    .get(&format!("http://{}:{}/api/v1/models", host, port))
    .header("Authorization", format!("Bearer {}", primary_access_token))
    .send()
    .await?;

  assert_eq!(response.status(), StatusCode::OK);

  // Test 2: Cross-client token should work via token exchange
  let response = reqwest::Client::new()
    .get(&format!("http://{}:{}/api/v1/models", host, port))
    .header("Authorization", format!("Bearer {}", cross_client_token))
    .send()
    .await?;

  assert_eq!(response.status(), StatusCode::OK);

  // Test 3: Verify caching - second request with same cross-client token should be faster
  let start = std::time::Instant::now();
  let response = reqwest::Client::new()
    .get(&format!("http://{}:{}/api/v1/models", host, port))
    .header("Authorization", format!("Bearer {}", cross_client_token))
    .send()
    .await?;
  let duration = start.elapsed();

  assert_eq!(response.status(), StatusCode::OK);
  assert!(duration < Duration::from_millis(50)); // Should be fast due to caching

  handle.abort();
  Ok(())
}

#[rstest::rstest]
#[awt]
#[tokio::test]
#[timeout(Duration::from_secs(5 * 60))]
#[serial_test::serial(live)]
async fn test_live_invalid_issuer_rejection(
  #[future] live_server: anyhow::Result<TestServerHandle>,
) -> anyhow::Result<()> {
  let TestServerHandle {
    temp_cache_dir: _temp_cache_dir,
    host,
    port,
    handle,
    app_service: _,
  } = live_server?;

  // Create a token with invalid issuer (manually crafted)
  let invalid_token = create_test_token_with_invalid_issuer();

  // Test: Invalid issuer token should be rejected
  let response = reqwest::Client::new()
    .get(&format!("http://{}:{}/api/v1/models", host, port))
    .header("Authorization", format!("Bearer {}", invalid_token))
    .send()
    .await?;

  assert_eq!(response.status(), StatusCode::UNAUTHORIZED);

  let error_body: Value = response.json().await?;
  assert_eq!(error_body["error"], "invalid_issuer");

  handle.abort();
  Ok(())
}

#[rstest::rstest]
#[awt]
#[tokio::test]
#[timeout(Duration::from_secs(5 * 60))]
#[serial_test::serial(live)]
async fn test_live_token_expiry_handling(
  #[future] live_server: anyhow::Result<TestServerHandle>,
) -> anyhow::Result<()> {
  let TestServerHandle {
    temp_cache_dir: _temp_cache_dir,
    host,
    port,
    handle,
    app_service: _,
  } = live_server?;

  // Create an expired token (manually crafted)
  let expired_token = create_test_expired_token();

  // Test: Expired token should return 401 with expiry information
  let response = reqwest::Client::new()
    .get(&format!("http://{}:{}/api/v1/models", host, port))
    .header("Authorization", format!("Bearer {}", expired_token))
    .send()
    .await?;

  assert_eq!(response.status(), StatusCode::UNAUTHORIZED);

  let error_body: Value = response.json().await?;
  assert_eq!(error_body["error"], "token_expired");
  assert_eq!(error_body["error_description"], "The provided token has expired");
  assert!(error_body["expires_at"].is_string());

  handle.abort();
  Ok(())
}

// Helper function to get tokens from a different client
async fn get_cross_client_oauth_tokens(app_service: &dyn AppService) -> anyhow::Result<(String, String)> {
  let setting_service = app_service.setting_service();
  let auth_url = setting_service.auth_url();
  let realm = setting_service.auth_realm();

  // Use different client credentials for cross-client testing
  let cross_client_id = std::env::var("INTEG_TEST_CROSS_CLIENT_ID")
    .map_err(|_| anyhow::anyhow!("INTEG_TEST_CROSS_CLIENT_ID not set"))?;
  let cross_client_secret = std::env::var("INTEG_TEST_CROSS_CLIENT_SECRET")
    .map_err(|_| anyhow::anyhow!("INTEG_TEST_CROSS_CLIENT_SECRET not set"))?;

  let username = std::env::var("INTEG_TEST_USERNAME")
    .map_err(|_| anyhow::anyhow!("INTEG_TEST_USERNAME not set"))?;
  let password = std::env::var("INTEG_TEST_PASSWORD")
    .map_err(|_| anyhow::anyhow!("INTEG_TEST_PASSWORD not set"))?;

  let token_url = format!(
    "{}/realms/{}/protocol/openid-connect/token",
    auth_url.trim_end_matches('/'),
    realm
  );

  let params = [
    ("grant_type", "password"),
    ("client_id", &cross_client_id),
    ("client_secret", &cross_client_secret),
    ("username", &username),
    ("password", &password),
    ("scope", &["openid", "email", "profile", "roles"].join(" ")),
  ];

  let client = reqwest::Client::new();
  let response = client.post(&token_url).form(&params).send().await?;
  assert_eq!(200, response.status());
  let token_data: Value = response.json().await?;
  let access_token = token_data["access_token"]
    .as_str()
    .ok_or_else(|| anyhow::anyhow!("Missing access_token in response"))?;
  let refresh_token = token_data["refresh_token"]
    .as_str()
    .ok_or_else(|| anyhow::anyhow!("Missing refresh_token in response"))?;

  Ok((access_token.to_string(), refresh_token.to_string()))
}

fn create_test_token_with_invalid_issuer() -> String {
  // Create a manually crafted token with invalid issuer for testing
  use jsonwebtoken::{encode, Header, EncodingKey};
  use serde_json::json;

  let claims = json!({
    "exp": (chrono::Utc::now() + chrono::Duration::hours(1)).timestamp(),
    "iat": chrono::Utc::now().timestamp(),
    "jti": uuid::Uuid::new_v4().to_string(),
    "iss": "https://malicious.example.com/realms/evil",  // Invalid issuer
    "sub": "test-user",
    "typ": "Bearer",
    "azp": "malicious-client",
    "scope": "openid profile email",
  });

  // Use test private key for signing
  let private_key = include_bytes!("../tests/data/test_private_key.pem");
  let encoding_key = EncodingKey::from_rsa_pem(private_key).unwrap();

  encode(&Header::new(jsonwebtoken::Algorithm::RS256), &claims, &encoding_key).unwrap()
}

fn create_test_expired_token() -> String {
  // Create a manually crafted expired token for testing
  use jsonwebtoken::{encode, Header, EncodingKey};
  use serde_json::json;

  let claims = json!({
    "exp": (chrono::Utc::now() - chrono::Duration::hours(1)).timestamp(), // Expired
    "iat": (chrono::Utc::now() - chrono::Duration::hours(2)).timestamp(),
    "jti": uuid::Uuid::new_v4().to_string(),
    "iss": "https://id.mydomain.com/realms/myapp",  // Valid issuer
    "sub": "test-user",
    "typ": "Bearer",
    "azp": "test-client",
    "scope": "openid profile email",
  });

  // Use test private key for signing
  let private_key = include_bytes!("../tests/data/test_private_key.pem");
  let encoding_key = EncodingKey::from_rsa_pem(private_key).unwrap();

  encode(&Header::new(jsonwebtoken::Algorithm::RS256), &claims, &encoding_key).unwrap()
}
```

## Middleware Integration

### Enhanced Auth Middleware with Cross-Client Support

```rust
// Enhanced auth_middleware.rs with feature flag support
pub async fn auth_middleware(
  session: Session,
  State(state): State<Arc<dyn RouterState>>,
  headers: HeaderMap,
  mut req: Request,
  next: Next,
) -> Result<Response, ApiError> {
  // ... existing code ...

  if let Some(header) = req.headers().get(axum::http::header::AUTHORIZATION) {
    let header = header.to_str()
      .map_err(|err| AuthError::InvalidToken(err.to_string()))?;

    // Get token service with cross-client support if enabled
    let token_service = if state.app_service().setting_service().token_exchange_enabled() {
      create_enhanced_token_service(state.app_service())
    } else {
      create_standard_token_service(state.app_service())
    };

    // Enhanced validation supports cross-client tokens
    let (access_token, token_scope) = token_service
      .validate_bearer_token(header)
      .await
      .map_err(|e| {
        tracing::warn!("Token validation failed: {}", e);
        match e {
          AuthError::TokenExpired { expires_at, .. } => {
            ApiError::Unauthorized {
              error: "token_expired".to_string(),
              error_description: "The provided token has expired".to_string(),
              expires_at: Some(expires_at),
            }
          }
          AuthError::InvalidIssuer { .. } => {
            ApiError::Unauthorized {
              error: "invalid_issuer".to_string(),
              error_description: "Token issuer not authorized".to_string(),
              expires_at: None,
            }
          }
          AuthError::TokenExchangeRateLimit => {
            ApiError::TooManyRequests {
              error: "rate_limit_exceeded".to_string(),
              error_description: "Too many token exchange requests".to_string(),
              retry_after: Some(60),
            }
          }
          _ => ApiError::Unauthorized {
            error: "invalid_token".to_string(),
            error_description: "Token validation failed".to_string(),
            expires_at: None,
          }
        }
      })?;

    // ... rest of middleware logic unchanged ...
  }

  // ... existing code ...
}

fn create_enhanced_token_service(app_service: &dyn AppService) -> DefaultTokenService {
  let settings = app_service.setting_service().token_exchange_settings();
  let issuer_validator = IssuerValidator::new(
    &app_service.setting_service().auth_url(),
    &app_service.setting_service().auth_realm(),
  );
  let rate_limiter = TokenExchangeRateLimiter::new(
    app_service.cache_service(),
    settings.rate_limit_per_minute,
  );

  DefaultTokenService::new(
    app_service.auth_service(),
    app_service.secret_service(),
    app_service.cache_service(),
    app_service.db_service(),
    issuer_validator,
    Arc::new(rate_limiter),
    settings,
  )
}

fn create_standard_token_service(app_service: &dyn AppService) -> DefaultTokenService {
  // Create token service without cross-client support for backward compatibility
  DefaultTokenService::new_standard(
    app_service.auth_service(),
    app_service.secret_service(),
    app_service.cache_service(),
    app_service.db_service(),
  )
}
```

## Related Documentation

- **[Functional Specification](20250628-token-exchange.md)** - Business requirements and user stories
- **[Authentication Architecture](../01-architecture/authentication.md)** - Current authentication system
- **[Auth Middleware](../03-crates/auth_middleware.md)** - Middleware implementation details
- **[Services Crate](../03-crates/services.md)** - Service layer architecture
- **[OAuth 2.0 Token Exchange RFC 8693](https://datatracker.ietf.org/doc/html/rfc8693)** - Official specification
- **[Keycloak Token Exchange Documentation](https://www.keycloak.org/securing-apps/token-exchange)** - Keycloak implementation details
