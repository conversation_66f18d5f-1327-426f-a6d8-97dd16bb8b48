# OAuth 2.0 Token Exchange for Cross-Client Token Validation

## Overview

This feature implements OAuth 2.0 Token Exchange (RFC 8693) to enable cross-client token validation in our Rust/Axum backend with Keycloak integration. The implementation allows tokens issued by the same Keycloak issuer but for different clients to be validated and exchanged for tokens valid for our client.

## Current State Analysis

### Existing Token Validation
- **Database-backed validation**: Tokens stored in `api_tokens` table with SHA-256 hash verification
- **Cache layer**: `MokaCacheService` for performance optimization
- **Single-client scope**: Only accepts tokens issued to our OAuth client
- **Keycloak integration**: Existing `AuthService::exchange_token` method for token exchange

### Current Architecture
```rust
// Current token validation flow
1. Extract token from Authorization header
2. Parse JWT claims (no signature verification)
3. Database lookup by token_id (jti claim)
4. SHA-256 hash verification for integrity
5. Business logic validation (iat, typ, azp, exp)
```

## Feature Requirements

### Functional Requirements

#### FR1: Cross-Client Token Acceptance
- **Requirement**: Accept tokens issued by same Keycloak issuer for different clients
- **Current Limitation**: Only accepts tokens where `azp` claim matches our client_id
- **Solution**: Validate issuer and realm instead of specific client

#### FR2: Token Exchange Implementation
- **Requirement**: Exchange third-party client tokens for our client tokens
- **Implementation**: Use existing `AuthService::exchange_token` method
- **Flow**: subject_token (third-party) → requested_token (our client)

#### FR3: Secure Token Caching
- **Requirement**: Cache exchanged tokens to avoid repeated exchanges
- **Security**: Use token ID and digest to prevent intrusion
- **Key Format**: `exchange:{subject_token_jti}:{subject_token_hash_prefix}`

#### FR4: Issuer Validation
- **Requirement**: Verify token issuer matches configured Keycloak server
- **Validation**: Compare `iss` claim against expected issuer URL
- **Security**: Prevent token acceptance from unauthorized issuers

### Non-Functional Requirements

#### NFR1: Security
- **Zero Trust**: All tokens must be validated against Keycloak
- **Tamper Prevention**: Use cryptographic hashes for cache keys
- **Audit Trail**: Log all token exchange operations
- **Rate Limiting**: Prevent abuse of token exchange endpoint

#### NFR2: Performance
- **Cache Efficiency**: Minimize repeated token exchanges
- **Database Optimization**: Efficient lookups for token validation
- **Async Operations**: Non-blocking token exchange calls

#### NFR3: Reliability
- **Error Handling**: Graceful degradation on exchange failures
- **Timeout Management**: Reasonable timeouts for Keycloak calls
- **Fallback Strategy**: Clear error messages for debugging

## Technical Implementation

### Enhanced Token Service Architecture

```rust
pub struct DefaultTokenService {
  auth_service: Arc<dyn AuthService>,
  secret_service: Arc<dyn SecretService>,
  cache_service: Arc<dyn CacheService>,
  db_service: Arc<dyn DbService>,
}

impl DefaultTokenService {
  // Enhanced validation supporting cross-client tokens
  pub async fn validate_bearer_token(&self, header: &str) -> Result<(String, TokenScope), AuthError> {
    let token = self.extract_bearer_token(header)?;
    
    // Try existing database validation first
    if let Ok(result) = self.validate_database_token(&token).await {
      return Ok(result);
    }
    
    // If not found, try cross-client token exchange
    self.validate_and_exchange_cross_client_token(&token).await
  }
  
  // New method for cross-client token validation and exchange
  async fn validate_and_exchange_cross_client_token(&self, token: &str) -> Result<(String, TokenScope), AuthError> {
    // 1. Parse JWT claims (no signature verification)
    let claims = extract_claims::<Claims>(token)?;
    
    // 2. Validate issuer matches our Keycloak instance
    self.validate_issuer(&claims.iss)?;
    
    // 3. Check cache for previously exchanged token
    let cache_key = self.generate_exchange_cache_key(&claims.jti, token)?;
    if let Some(cached_token) = self.cache_service.get(&cache_key) {
      if let Ok(validated) = self.validate_cached_exchange_token(&cached_token).await {
        return Ok(validated);
      }
    }
    
    // 4. Exchange token with Keycloak
    let exchanged_token = self.exchange_cross_client_token(token).await?;
    
    // 5. Cache the exchanged token
    self.cache_exchanged_token(&cache_key, &exchanged_token).await?;
    
    // 6. Return validated token and scope
    self.extract_token_scope(&exchanged_token).await
  }
}
```

### Enhanced Token Caching Strategy

```rust
impl DefaultTokenService {
  // Generate secure cache key for exchanged tokens
  fn generate_exchange_cache_key(&self, jti: &str, token: &str) -> Result<String, AuthError> {
    use sha2::{Digest, Sha256};

    // Create composite hash from token content and metadata
    let token_hash = format!("{:x}", Sha256::digest(token.as_bytes()));
    let hash_prefix = &token_hash[..16]; // 16 chars for better collision resistance

    // Include timestamp to prevent long-term cache poisoning
    let now = Utc::now().timestamp();
    let time_bucket = now / 3600; // 1-hour buckets for cache rotation

    Ok(format!("exchange:{}:{}:{}", jti, hash_prefix, time_bucket))
  }

  // Enhanced cache structure with security metadata
  #[derive(Serialize, Deserialize)]
  struct ExchangeTokenCache {
    access_token: String,
    exp: u64,
    cached_at: u64,
    subject_client: String,
    subject_jti: String,
    exchange_count: u32,
    last_validated: u64,
  }

  // Cache exchanged token with comprehensive metadata
  async fn cache_exchanged_token(&self, cache_key: &str, token: &str, subject_token: &str) -> Result<(), AuthError> {
    // Parse both tokens for metadata
    let exchanged_claims = extract_claims::<Claims>(token)?;
    let subject_claims = extract_claims::<Claims>(subject_token)?;
    let now = Utc::now().timestamp() as u64;

    // Security checks before caching
    if exchanged_claims.exp <= now + 300 { // Don't cache tokens expiring in < 5 minutes
      return Ok(());
    }

    // Create cache entry with security metadata
    let cache_entry = ExchangeTokenCache {
      access_token: token.to_string(),
      exp: exchanged_claims.exp,
      cached_at: now,
      subject_client: subject_claims.azp.clone(),
      subject_jti: subject_claims.jti.clone(),
      exchange_count: 1,
      last_validated: now,
    };

    // Serialize and cache
    let cache_value = serde_json::to_string(&cache_entry)?;
    self.cache_service.set(cache_key, &cache_value);

    // Log cache operation for audit
    tracing::info!(
      "Cached exchanged token: subject_client={}, subject_jti={}, exp={}",
      cache_entry.subject_client,
      cache_entry.subject_jti,
      cache_entry.exp
    );

    Ok(())
  }

  // Validate cached exchanged token with security checks
  async fn validate_cached_exchange_token(&self, cached_value: &str, cache_key: &str) -> Result<(String, TokenScope), AuthError> {
    let mut cache_entry: ExchangeTokenCache = serde_json::from_str(cached_value)
      .map_err(|_| AuthError::InvalidCacheData)?;

    let now = Utc::now().timestamp() as u64;

    // Security validations
    if cache_entry.exp <= now + 60 { // 1 minute buffer for expiration
      self.cache_service.remove(cache_key);
      return Err(AuthError::TokenExpired);
    }

    // Check for suspicious usage patterns
    if cache_entry.exchange_count > 1000 { // Prevent abuse
      tracing::warn!(
        "High exchange count detected: subject_jti={}, count={}",
        cache_entry.subject_jti,
        cache_entry.exchange_count
      );
      self.cache_service.remove(cache_key);
      return Err(AuthError::SuspiciousActivity);
    }

    // Update usage statistics
    cache_entry.exchange_count += 1;
    cache_entry.last_validated = now;

    // Re-cache updated entry
    let updated_cache = serde_json::to_string(&cache_entry)?;
    self.cache_service.set(cache_key, &updated_cache);

    // Extract scope from cached token
    let claims = extract_claims::<Claims>(&cache_entry.access_token)?;
    let scope = TokenScope::from_scope(&claims.scope)?;

    tracing::debug!(
      "Cache hit for exchanged token: subject_jti={}, usage_count={}",
      cache_entry.subject_jti,
      cache_entry.exchange_count
    );

    Ok((cache_entry.access_token, scope))
  }

  // Cache cleanup for expired entries
  async fn cleanup_expired_exchange_cache(&self) -> Result<(), AuthError> {
    // Note: This would require cache enumeration capability
    // For now, rely on cache TTL and periodic cleanup
    tracing::debug!("Exchange cache cleanup completed");
    Ok(())
  }

  // Security validation for cache key integrity
  fn validate_cache_key_integrity(&self, cache_key: &str, original_token: &str) -> Result<(), AuthError> {
    // Extract components from cache key
    let parts: Vec<&str> = cache_key.split(':').collect();
    if parts.len() != 4 || parts[0] != "exchange" {
      return Err(AuthError::InvalidCacheKey);
    }

    let cached_jti = parts[1];
    let cached_hash_prefix = parts[2];

    // Validate against original token
    let claims = extract_claims::<Claims>(original_token)?;
    if claims.jti != cached_jti {
      return Err(AuthError::CacheKeyMismatch);
    }

    // Validate hash prefix
    use sha2::{Digest, Sha256};
    let token_hash = format!("{:x}", Sha256::digest(original_token.as_bytes()));
    let expected_prefix = &token_hash[..16];

    if cached_hash_prefix != expected_prefix {
      return Err(AuthError::CacheKeyMismatch);
    }

    Ok(())
  }
}
```

### Issuer Validation

```rust
impl DefaultTokenService {
  // Validate token issuer against configured Keycloak instance
  fn validate_issuer(&self, token_issuer: &str) -> Result<(), AuthError> {
    let app_reg_info = self.secret_service
      .app_reg_info()?
      .ok_or(AppRegInfoMissingError)?;
    
    // Get expected issuer from settings
    let expected_issuer = self.build_expected_issuer()?;
    
    if token_issuer != expected_issuer {
      return Err(AuthError::InvalidIssuer {
        expected: expected_issuer,
        actual: token_issuer.to_string(),
      });
    }
    
    Ok(())
  }
  
  // Build expected issuer URL from settings
  fn build_expected_issuer(&self) -> Result<String, AuthError> {
    // Extract from existing auth service configuration
    // Format: https://{auth_url}/realms/{realm}
    let auth_url = self.secret_service.auth_url()?;
    let auth_realm = self.secret_service.auth_realm()?;
    
    Ok(format!("{}/realms/{}", auth_url.trim_end_matches('/'), auth_realm))
  }
}
```

### Cross-Client Token Exchange

```rust
impl DefaultTokenService {
  // Exchange cross-client token for our client token
  async fn exchange_cross_client_token(&self, subject_token: &str) -> Result<String, AuthError> {
    let app_reg_info = self.secret_service
      .app_reg_info()?
      .ok_or(AppRegInfoMissingError)?;
    
    // Use existing AuthService::exchange_token method
    let (access_token, _refresh_token) = self.auth_service
      .exchange_token(
        &app_reg_info.client_id,
        &app_reg_info.client_secret,
        subject_token,
        "urn:ietf:params:oauth:token-type:access_token",
        vec!["openid".to_string()], // Minimal scope for validation
      )
      .await?;
    
    Ok(access_token)
  }
}
```

## Error Handling

### Enhanced Error Types

```rust
#[derive(Debug, thiserror::Error, errmeta_derive::ErrorMeta)]
#[error_meta(trait_to_impl = AppError)]
pub enum AuthError {
  // Existing errors...

  #[error("invalid issuer: expected {expected}, got {actual}")]
  #[error_meta(error_type = ErrorType::Authentication)]
  InvalidIssuer { expected: String, actual: String },

  #[error("token exchange failed: {0}")]
  #[error_meta(error_type = ErrorType::Authentication)]
  TokenExchangeFailed(String),

  #[error("invalid cache data format")]
  #[error_meta(error_type = ErrorType::InternalServerError)]
  InvalidCacheData,

  #[error("cross-client token validation failed: {0}")]
  #[error_meta(error_type = ErrorType::Authentication)]
  CrossClientValidationFailed(String),

  #[error("token expired")]
  #[error_meta(error_type = ErrorType::Authentication)]
  TokenExpired,

  #[error("suspicious activity detected")]
  #[error_meta(error_type = ErrorType::Authentication)]
  SuspiciousActivity,

  #[error("invalid cache key format")]
  #[error_meta(error_type = ErrorType::InternalServerError)]
  InvalidCacheKey,

  #[error("cache key mismatch with token")]
  #[error_meta(error_type = ErrorType::Authentication)]
  CacheKeyMismatch,

  #[error("rate limit exceeded for token exchange")]
  #[error_meta(error_type = ErrorType::RateLimit)]
  TokenExchangeRateLimit,

  #[error("issuer configuration not found")]
  #[error_meta(error_type = ErrorType::Configuration)]
  IssuerConfigurationMissing,
}
```

### Error Response Handling

```rust
impl DefaultTokenService {
  // Handle token exchange errors gracefully
  async fn handle_exchange_error(&self, error: AuthServiceError) -> AuthError {
    match error {
      AuthServiceError::TokenExchangeError(msg) if msg.contains("invalid_grant") => {
        AuthError::CrossClientValidationFailed("Token not valid for exchange".to_string())
      }
      AuthServiceError::TokenExchangeError(msg) if msg.contains("invalid_client") => {
        AuthError::CrossClientValidationFailed("Client not authorized for exchange".to_string())
      }
      _ => AuthError::TokenExchangeFailed(error.to_string()),
    }
  }
}
```

## Security Considerations

### Token Validation Security
1. **Issuer Verification**: Strict validation against configured Keycloak instance
2. **Cache Key Security**: Cryptographic hash prevents cache poisoning
3. **Token Expiration**: Respect token expiration times with safety buffers
4. **Audit Logging**: Log all cross-client token validation attempts

### Attack Prevention
1. **Token Replay**: Cache keys include token hash to prevent replay
2. **Privilege Escalation**: Exchange only for equivalent or lesser scopes
3. **Rate Limiting**: Implement rate limiting on token exchange endpoint
4. **Monitoring**: Alert on unusual token exchange patterns

### Data Protection
1. **Sensitive Data**: Never log full tokens, only token IDs
2. **Cache Encryption**: Consider encrypting cached token data
3. **Memory Safety**: Clear sensitive data from memory promptly
4. **Transport Security**: Ensure HTTPS for all Keycloak communication

## Testing Strategy

### Unit Tests
```rust
#[cfg(test)]
mod tests {
  use super::*;
  use mockall::predicate::*;
  
  #[tokio::test]
  async fn test_cross_client_token_validation_success() {
    // Test successful cross-client token validation and exchange
  }
  
  #[tokio::test]
  async fn test_invalid_issuer_rejection() {
    // Test rejection of tokens from wrong issuer
  }
  
  #[tokio::test]
  async fn test_cache_key_generation() {
    // Test secure cache key generation
  }
  
  #[tokio::test]
  async fn test_cached_token_validation() {
    // Test validation of cached exchanged tokens
  }
}
```

### Integration Tests
```rust
#[tokio::test]
async fn test_end_to_end_cross_client_flow() {
  // Test complete flow from third-party token to validated access
}

#[tokio::test]
async fn test_keycloak_integration() {
  // Test actual token exchange with Keycloak test instance
}
```

## Detailed Implementation Plan

### Phase 1: Core Infrastructure (Week 1-2)

#### 1.1 Enhanced Error Types
**Files**: `crates/auth_middleware/src/lib.rs`
```rust
// Add new error variants to AuthError enum
// Implement proper error metadata with errmeta_derive
// Add error conversion traits for seamless integration
```

#### 1.2 Issuer Validation Service
**Files**: `crates/auth_middleware/src/issuer_validator.rs` (new)
```rust
pub struct IssuerValidator {
  expected_issuer: String,
  realm: String,
}

impl IssuerValidator {
  pub fn new(auth_url: &str, realm: &str) -> Self;
  pub fn validate(&self, token_issuer: &str) -> Result<(), AuthError>;
  pub fn build_expected_issuer(&self) -> String;
}
```

#### 1.3 Enhanced Token Service Structure
**Files**: `crates/auth_middleware/src/token_service.rs`
```rust
// Add new fields to DefaultTokenService
pub struct DefaultTokenService {
  auth_service: Arc<dyn AuthService>,
  secret_service: Arc<dyn SecretService>,
  cache_service: Arc<dyn CacheService>,
  db_service: Arc<dyn DbService>,
  issuer_validator: IssuerValidator,  // New field
  rate_limiter: Arc<dyn RateLimiter>, // New field
}
```

#### 1.4 Unit Tests Foundation
**Files**: `crates/auth_middleware/src/token_service.rs`
- Test issuer validation logic
- Test cache key generation
- Test error handling scenarios
- Mock services for isolated testing

### Phase 2: Token Exchange Integration (Week 3-4)

#### 2.1 Cross-Client Token Validation
**Files**: `crates/auth_middleware/src/token_service.rs`
```rust
impl DefaultTokenService {
  // Main entry point - enhanced validation
  pub async fn validate_bearer_token(&self, header: &str) -> Result<(String, TokenScope), AuthError>;

  // New cross-client validation flow
  async fn validate_and_exchange_cross_client_token(&self, token: &str) -> Result<(String, TokenScope), AuthError>;

  // Issuer validation
  fn validate_token_issuer(&self, claims: &Claims) -> Result<(), AuthError>;
}
```

#### 2.2 Secure Caching Implementation
**Files**: `crates/auth_middleware/src/exchange_cache.rs` (new)
```rust
pub struct ExchangeTokenCache {
  access_token: String,
  exp: u64,
  cached_at: u64,
  subject_client: String,
  subject_jti: String,
  exchange_count: u32,
  last_validated: u64,
}

pub struct ExchangeCacheManager {
  cache_service: Arc<dyn CacheService>,
  settings: ExchangeCacheSettings,
}

impl ExchangeCacheManager {
  pub fn generate_cache_key(&self, jti: &str, token: &str) -> Result<String, AuthError>;
  pub async fn cache_token(&self, key: &str, token: &str, subject: &str) -> Result<(), AuthError>;
  pub async fn get_cached_token(&self, key: &str) -> Result<Option<ExchangeTokenCache>, AuthError>;
  pub fn validate_cache_integrity(&self, key: &str, token: &str) -> Result<(), AuthError>;
}
```

#### 2.3 Rate Limiting Implementation
**Files**: `crates/auth_middleware/src/rate_limiter.rs` (new)
```rust
#[async_trait]
pub trait RateLimiter: Send + Sync + std::fmt::Debug {
  async fn check_rate_limit(&self, client_id: &str, operation: &str) -> Result<(), AuthError>;
  async fn record_operation(&self, client_id: &str, operation: &str) -> Result<(), AuthError>;
}

pub struct TokenExchangeRateLimiter {
  cache_service: Arc<dyn CacheService>,
  max_requests_per_minute: u32,
}
```

#### 2.4 Integration Tests
**Files**: `crates/auth_middleware/tests/integration_tests.rs`
- End-to-end token exchange flow
- Cache behavior validation
- Rate limiting verification
- Error scenario testing

### Phase 3: Security Hardening (Week 5)

#### 3.1 Audit Logging
**Files**: `crates/auth_middleware/src/audit_logger.rs` (new)
```rust
pub struct TokenExchangeAuditLogger {
  logger: Arc<dyn Logger>,
}

impl TokenExchangeAuditLogger {
  pub fn log_exchange_attempt(&self, subject_client: &str, result: &Result<(), AuthError>);
  pub fn log_cache_operation(&self, operation: &str, cache_key: &str);
  pub fn log_rate_limit_violation(&self, client_id: &str);
  pub fn log_security_event(&self, event_type: &str, details: &str);
}
```

#### 3.2 Security Monitoring
**Files**: `crates/auth_middleware/src/security_monitor.rs` (new)
```rust
pub struct SecurityMonitor {
  metrics: Arc<dyn MetricsCollector>,
  alert_thresholds: SecurityThresholds,
}

impl SecurityMonitor {
  pub fn record_exchange_success(&self, client_id: &str);
  pub fn record_exchange_failure(&self, client_id: &str, error: &AuthError);
  pub fn check_suspicious_patterns(&self, client_id: &str) -> Result<(), AuthError>;
}
```

#### 3.3 Performance Optimization
- Cache hit ratio optimization
- Token validation latency reduction
- Memory usage optimization
- Database query optimization

#### 3.4 Security Testing
- Penetration testing scenarios
- Token replay attack prevention
- Cache poisoning prevention
- Rate limiting bypass attempts

### Phase 4: Configuration and Deployment (Week 6)

#### 4.1 Configuration Management
**Files**: `crates/services/src/setting_service.rs`
```rust
impl SettingService {
  // Token exchange configuration
  pub fn token_exchange_enabled(&self) -> bool;
  pub fn token_exchange_cache_ttl(&self) -> u64;
  pub fn token_exchange_rate_limit(&self) -> u32;
  pub fn token_exchange_max_cache_size(&self) -> usize;

  // Security configuration
  pub fn token_exchange_audit_enabled(&self) -> bool;
  pub fn token_exchange_suspicious_threshold(&self) -> u32;
}
```

#### 4.2 Middleware Integration
**Files**: `crates/auth_middleware/src/auth_middleware.rs`
```rust
// Enhanced middleware with feature flag support
pub async fn auth_middleware(
  session: Session,
  State(state): State<Arc<dyn RouterState>>,
  headers: HeaderMap,
  mut req: Request,
  next: Next,
) -> Result<Response, ApiError> {
  // Check if token exchange is enabled
  let token_exchange_enabled = state.app_service()
    .setting_service()
    .token_exchange_enabled();

  // Create token service with appropriate configuration
  let token_service = if token_exchange_enabled {
    DefaultTokenService::with_cross_client_support(/* ... */)
  } else {
    DefaultTokenService::new(/* ... */)
  };

  // ... rest of middleware logic
}
```

#### 4.3 Documentation Updates
- API documentation updates
- Security considerations documentation
- Deployment guide updates
- Troubleshooting guide

#### 4.4 Monitoring and Alerting Setup
- Metrics collection configuration
- Alert threshold configuration
- Dashboard setup for monitoring
- Log aggregation setup

### Implementation Dependencies

#### External Dependencies
- **Keycloak Server**: Must support OAuth 2.0 Token Exchange (RFC 8693)
- **Network Connectivity**: Reliable connection to Keycloak for token exchange
- **Configuration Management**: Environment variables or config files

#### Internal Dependencies
- **Existing AuthService**: Token exchange method already implemented
- **Cache Service**: MokaCacheService for token caching
- **Database Service**: For token validation and storage
- **Settings Service**: For configuration management

### Risk Mitigation

#### Technical Risks
1. **Keycloak Compatibility**: Verify token exchange support in target Keycloak version
2. **Performance Impact**: Monitor token exchange latency and cache efficiency
3. **Cache Memory Usage**: Implement cache size limits and cleanup

#### Security Risks
1. **Token Leakage**: Ensure tokens are never logged in full
2. **Cache Poisoning**: Validate cache key integrity
3. **Rate Limiting Bypass**: Implement multiple rate limiting strategies

#### Operational Risks
1. **Configuration Errors**: Provide clear configuration validation
2. **Monitoring Gaps**: Ensure comprehensive metrics and alerting
3. **Rollback Strategy**: Support disabling feature via configuration

### Success Criteria

#### Functional Success
- [ ] Cross-client tokens validated successfully
- [ ] Token exchange caching reduces Keycloak calls by >80%
- [ ] Rate limiting prevents abuse scenarios
- [ ] Error handling provides clear feedback

#### Performance Success
- [ ] Token validation latency < 100ms (95th percentile)
- [ ] Cache hit ratio > 80% for repeated tokens
- [ ] Memory usage increase < 50MB under normal load
- [ ] No degradation in existing token validation performance

#### Security Success
- [ ] All security tests pass
- [ ] Audit logging captures all relevant events
- [ ] Rate limiting prevents DoS scenarios
- [ ] No token leakage in logs or error messages

## Monitoring and Observability

### Metrics
- Token exchange success/failure rates
- Cache hit/miss ratios for exchanged tokens
- Token validation latency
- Cross-client token usage patterns

### Logging
- All token exchange attempts (success/failure)
- Invalid issuer detection
- Cache operations for exchanged tokens
- Performance metrics for token validation

### Alerts
- High token exchange failure rates
- Unusual cross-client token patterns
- Cache performance degradation
- Keycloak connectivity issues

## Configuration

### Environment Variables
```bash
# Keycloak configuration (existing)
KEYCLOAK_URL=https://keycloak.example.com
KEYCLOAK_REALM=bodhi
KEYCLOAK_CLIENT_ID=bodhi-app
KEYCLOAK_CLIENT_SECRET=your-client-secret

# Token exchange configuration (new)
TOKEN_EXCHANGE_ENABLED=true
TOKEN_EXCHANGE_CACHE_TTL=3600  # seconds
TOKEN_EXCHANGE_RATE_LIMIT=100  # requests per minute per client
```

### Settings Service Integration
```rust
impl SettingService {
  pub fn token_exchange_enabled(&self) -> bool {
    self.get_bool("TOKEN_EXCHANGE_ENABLED").unwrap_or(true)
  }

  pub fn token_exchange_cache_ttl(&self) -> u64 {
    self.get_u64("TOKEN_EXCHANGE_CACHE_TTL").unwrap_or(3600)
  }

  pub fn token_exchange_rate_limit(&self) -> u32 {
    self.get_u32("TOKEN_EXCHANGE_RATE_LIMIT").unwrap_or(100)
  }
}
```

## Database Schema Changes

### No Schema Changes Required
The existing `api_tokens` table structure supports the token exchange feature without modifications:

```sql
-- Existing table supports cross-client tokens
CREATE TABLE api_tokens (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,           -- Subject from any client
    name TEXT DEFAULT '',
    token_id TEXT NOT NULL UNIQUE,   -- JWT ID (jti) from any issuer
    token_hash TEXT NOT NULL,        -- SHA-256 hash for integrity
    status TEXT NOT NULL CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Cache Schema
```rust
// Cache key format for exchanged tokens
// Key: "exchange:{subject_jti}:{subject_hash_prefix}"
// Value: JSON with exchanged token data
{
  "access_token": "eyJhbGciOiJSUzI1NiIs...",
  "exp": 1640995200,
  "cached_at": 1640991600,
  "subject_client": "third-party-client-id"
}
```

## API Changes

### No Public API Changes
The token exchange feature is transparent to API consumers:

- **Existing endpoints**: Continue to work without changes
- **Authentication headers**: Same `Authorization: Bearer <token>` format
- **Error responses**: Enhanced with cross-client validation errors
- **Rate limiting**: Applied transparently to token exchange operations

### Internal Service Interface
```rust
// Enhanced AuthService trait (no breaking changes)
#[async_trait]
pub trait AuthService: Send + Sync + std::fmt::Debug {
  // Existing methods unchanged...

  // Enhanced exchange_token method supports cross-client scenarios
  async fn exchange_token(
    &self,
    client_id: &str,
    client_secret: &str,
    subject_token: &str,
    token_type: &str,
    scopes: Vec<String>,
  ) -> Result<(String, Option<String>)>;
}
```

## Deployment Considerations

### Backward Compatibility
- **Zero Breaking Changes**: Existing token validation continues to work
- **Gradual Rollout**: Feature can be enabled/disabled via configuration
- **Fallback Strategy**: Falls back to existing validation if exchange fails

### Performance Impact
- **Cache Efficiency**: Reduces repeated token exchanges
- **Network Overhead**: Additional Keycloak calls for new token types
- **Memory Usage**: Minimal increase for cache storage

### Security Deployment
- **Rate Limiting**: Configure appropriate limits for production
- **Monitoring**: Set up alerts for unusual token exchange patterns
- **Audit Logging**: Ensure compliance with security requirements

## Code Examples

### Usage in Middleware
```rust
// auth_middleware.rs - Enhanced token validation
pub async fn auth_middleware(
  session: Session,
  State(state): State<Arc<dyn RouterState>>,
  headers: HeaderMap,
  mut req: Request,
  next: Next,
) -> Result<Response, ApiError> {
  // ... existing code ...

  if let Some(header) = req.headers().get(axum::http::header::AUTHORIZATION) {
    let header = header.to_str()
      .map_err(|err| AuthError::InvalidToken(err.to_string()))?;

    // Enhanced validation supports cross-client tokens
    let (access_token, token_scope) = token_service
      .validate_bearer_token(header)
      .await?;

    // ... rest of middleware logic unchanged ...
  }

  // ... existing code ...
}
```

### Error Handling Example
```rust
// Example of handling cross-client token validation
match token_service.validate_bearer_token(&auth_header).await {
  Ok((token, scope)) => {
    // Token validated successfully (could be cross-client)
    proceed_with_request(token, scope).await
  }
  Err(AuthError::InvalidIssuer { expected, actual }) => {
    log::warn!("Token from unauthorized issuer: {} (expected: {})", actual, expected);
    return Err(ApiError::Unauthorized);
  }
  Err(AuthError::CrossClientValidationFailed(msg)) => {
    log::info!("Cross-client token validation failed: {}", msg);
    return Err(ApiError::Unauthorized);
  }
  Err(e) => {
    log::error!("Token validation error: {}", e);
    return Err(ApiError::InternalServerError);
  }
}
```

## Related Documentation

- **[Authentication Architecture](../01-architecture/authentication.md)** - Current authentication system
- **[API Integration](../01-architecture/api-integration.md)** - Backend integration patterns
- **[Auth Middleware](../03-crates/auth_middleware.md)** - Middleware implementation details
- **[Services Crate](../03-crates/services.md)** - Service layer architecture
- **[OAuth 2.0 Token Exchange RFC 8693](https://datatracker.ietf.org/doc/html/rfc8693)** - Official specification
- **[Keycloak Token Exchange Documentation](https://www.keycloak.org/securing-apps/token-exchange)** - Keycloak implementation details
